{"name": "<PERSON>n<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "🤖 Intelligent code generation, refactoring, and documentation with AI - Beautiful UI and secure API key management", "version": "1.0.0", "publisher": "<PERSON>n<PERSON><PERSON><PERSON>", "icon": "assets/icon.png", "repository": {"type": "git", "url": "https://github.com/your-username/codenjkoiin"}, "keywords": ["ai", "artificial intelligence", "code generation", "openai", "gpt", "claude", "refactoring", "documentation", "assistant", "productivity"], "engines": {"vscode": "^1.102.0"}, "categories": ["Machine Learning", "Programming Languages", "Snippets", "Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aiCodeAssistant.generateCode", "title": "🤖 Generate Code", "category": "AI Assistant"}, {"command": "aiCodeAssistant.explainCode", "title": "💡 Explain Code", "category": "AI Assistant"}, {"command": "aiCodeAssistant.refactorCode", "title": "🔄 Refactor Code", "category": "AI Assistant"}, {"command": "aiCodeAssistant.generateDocumentation", "title": "📝 Generate Documentation", "category": "AI Assistant"}, {"command": "aiCodeAssistant.openChat", "title": "💬 Open AI Chat", "category": "AI Assistant"}, {"command": "aiCodeAssistant.configureApiKey", "title": "🔐 Configure API Key", "category": "AI Assistant"}, {"command": "aiCodeAssistant.optimizeCode", "title": "⚡ Optimize Code", "category": "AI Assistant"}], "menus": {"editor/context": [{"submenu": "aiCodeAssistant.submenu", "group": "1_modification@1"}], "aiCodeAssistant.submenu": [{"command": "aiCodeAssistant.generateCode", "group": "generation@1"}, {"command": "aiCodeAssistant.explainCode", "group": "analysis@1", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.refactorCode", "group": "modification@1", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.optimizeCode", "group": "modification@2", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.generateDocumentation", "group": "documentation@1", "when": "editorHasSelection"}], "commandPalette": [{"command": "aiCodeAssistant.generateCode"}, {"command": "aiCodeAssistant.explainCode", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.refactorCode", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.generateDocumentation", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.openChat"}, {"command": "aiCodeAssistant.configureApiKey"}, {"command": "aiCodeAssistant.optimizeCode", "when": "editorHasSelection"}]}, "submenus": [{"id": "aiCodeAssistant.submenu", "label": "🤖 AI Assistant", "icon": "$(robot)"}], "configuration": {"title": "AI Code Assistant", "properties": {"aiCodeAssistant.provider": {"type": "string", "enum": ["openai", "anthropic", "custom"], "default": "openai", "description": "AI provider to use for code generation", "enumDescriptions": ["OpenAI GPT models (GPT-4, GPT-3.5)", "Anthropic Claude models", "Custom API endpoint"]}, "aiCodeAssistant.model": {"type": "string", "default": "gpt-4", "description": "AI model to use (e.g., gpt-4, gpt-3.5-turbo, claude-3-sonnet)"}, "aiCodeAssistant.maxTokens": {"type": "number", "default": 2048, "minimum": 100, "maximum": 8192, "description": "Maximum number of tokens for AI responses"}, "aiCodeAssistant.temperature": {"type": "number", "default": 0.3, "minimum": 0, "maximum": 2, "description": "Creativity level (0 = focused, 2 = very creative)"}, "aiCodeAssistant.customEndpoint": {"type": "string", "default": "", "description": "Custom API endpoint URL (when using custom provider)"}, "aiCodeAssistant.includeContext": {"type": "boolean", "default": true, "description": "Include surrounding code context in AI requests"}, "aiCodeAssistant.contextLines": {"type": "number", "default": 10, "minimum": 0, "maximum": 50, "description": "Number of context lines to include before and after selection"}, "aiCodeAssistant.autoSave": {"type": "boolean", "default": false, "description": "Automatically save files after AI modifications"}, "aiCodeAssistant.showProgress": {"type": "boolean", "default": true, "description": "Show progress indicators during AI operations"}}}, "viewsContainers": {"activitybar": [{"id": "aiCodeAssistant", "title": "AI Code Assistant", "icon": "$(robot)"}]}, "views": {"aiCodeAssistant": [{"id": "aiCodeAssistant.chatView", "name": "AI Chat", "type": "webview"}, {"id": "aiCodeAssistant.historyView", "name": "Generation History", "type": "tree"}]}, "keybindings": [{"command": "aiCodeAssistant.generateCode", "key": "ctrl+shift+g", "mac": "cmd+shift+g", "when": "editorTextFocus"}, {"command": "aiCodeAssistant.explainCode", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorHasSelection"}, {"command": "aiCodeAssistant.openChat", "key": "ctrl+shift+c", "mac": "cmd+shift+c"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test", "package": "vsce package", "deploy": "vsce publish"}, "dependencies": {"axios": "^1.11.0", "marked": "^9.1.6"}, "devDependencies": {"@types/marked": "^6.0.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.102.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^2.32.0", "eslint": "^9.25.1", "typescript": "^5.8.3"}}