{"version": 3, "file": "clientCertificateCredential.d.ts", "sourceRoot": "", "sources": ["../../../src/credentials/clientCertificateCredential.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAStF,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,KAAK,EAAE,kCAAkC,EAAE,MAAM,yCAAyC,CAAC;AAIlG,OAAO,KAAK,EACV,2CAA2C,EAC3C,+BAA+B,EAC/B,mCAAmC,EACpC,MAAM,wCAAwC,CAAC;AAKhD;;;;;;;GAOG;AACH,qBAAa,2BAA4B,YAAW,eAAe;IACjE,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,4BAA4B,CAAW;IAC/C,OAAO,CAAC,wBAAwB,CAA8C;IAC9E,OAAO,CAAC,oBAAoB,CAAC,CAAU;IACvC,OAAO,CAAC,UAAU,CAAa;IAE/B;;;;;;;;;OASG;gBAED,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,eAAe,EAAE,MAAM,EACvB,OAAO,CAAC,EAAE,kCAAkC;IAE9C;;;;;;;;;OASG;gBAED,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,aAAa,EAAE,mCAAmC,EAClD,OAAO,CAAC,EAAE,kCAAkC;IAE9C;;;;;;;;;OASG;gBAED,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,aAAa,EAAE,+BAA+B,EAC9C,OAAO,CAAC,EAAE,kCAAkC;IA+C9C;;;;;;;OAOG;IACG,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,OAAO,GAAE,eAAoB,GAAG,OAAO,CAAC,WAAW,CAAC;YAehF,sBAAsB;CA6BrC;AAED;;;;;;GAMG;AACH,wBAAsB,gBAAgB,CACpC,wBAAwB,EAAE,2CAA2C,EACrE,oBAAoB,EAAE,OAAO,GAC5B,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,YAAY,CAAC,GAAG;IAAE,mBAAmB,EAAE,MAAM,CAAA;CAAE,CAAC,CAwCjF"}