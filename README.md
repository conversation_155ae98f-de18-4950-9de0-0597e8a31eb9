# 🤖 Codenjkoiin

**Intelligent code generation, refactoring, and documentation with AI - Beautiful UI and secure API key management**

Transform your coding experience with the power of artificial intelligence! Codenjkoiin brings cutting-edge AI capabilities directly into Visual Studio Code with a beautiful, intuitive interface and enterprise-grade security.

## ✨ Features

### 🚀 **Intelligent Code Generation**
- Generate complete functions, classes, and modules from natural language descriptions
- Context-aware code completion that understands your project structure
- Support for 15+ programming languages including JavaScript, Python, Java, C#, Go, Rust, and more
- Smart language detection based on your prompts

### 💬 **Interactive AI Chat**
- Beautiful chat interface with syntax highlighting
- Real-time code assistance and debugging help
- Persistent conversation history
- One-click code insertion from chat responses

### 🔄 **Advanced Code Operations**
- **Code Explanation**: Get detailed explanations of complex code segments
- **Smart Refactoring**: Improve code quality with AI-powered suggestions
- **Documentation Generation**: Auto-generate comprehensive documentation
- **Performance Optimization**: Get suggestions for better performance

### 🎨 **Beautiful User Interface**
- Modern, responsive design with smooth animations
- Dark/light theme support that follows VS Code preferences
- Intuitive icons and visual feedback
- Mobile-friendly responsive layout

### 🔐 **Enterprise-Grade Security**
- Secure API key storage using VS Code's built-in secrets management
- Support for multiple AI providers (OpenAI, Anthropic, Custom endpoints)
- No data logging or external storage of your code
- Configurable privacy settings

### 📊 **Smart Context Awareness**
- Automatically includes relevant code context in AI requests
- Function and class context detection
- Import statement analysis
- Configurable context window size

## 🚀 Quick Start

### 1. Install the Extension
Install from the VS Code Marketplace or download the `.vsix` file.

### 2. Configure Your AI Provider
1. Open the Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
2. Run `AI Assistant: Configure API Key`
3. Choose your preferred AI provider:
   - **OpenAI**: GPT-4, GPT-3.5-turbo models
   - **Anthropic**: Claude models
   - **Custom**: Your own API endpoint
4. Enter your API key securely

### 3. Start Generating Code!
- **Quick Generation**: `Ctrl+Shift+G` / `Cmd+Shift+G`
- **Open Chat**: `Ctrl+Shift+C` / `Cmd+Shift+C`
- **Explain Code**: Select code and press `Ctrl+Shift+E` / `Cmd+Shift+E`

## 📖 Usage Guide

### Code Generation
1. Open the code generator with `Ctrl+Shift+G` or from the Command Palette
2. Describe what you want to generate in natural language
3. Select the programming language (or let AI auto-detect)
4. Choose whether to include editor context for better results
5. Click "Generate Code" and review the results
6. Insert directly into your editor or copy to clipboard

**Example prompts:**
- "Create a React component for a user profile card with avatar, name, and bio"
- "Write a Python function to sort a list of dictionaries by multiple keys"
- "Generate a REST API endpoint for user authentication with JWT"

### Interactive Chat
1. Open the chat panel with `Ctrl+Shift+C`
2. Ask questions about programming, debugging, or code review
3. Get instant responses with syntax-highlighted code
4. Click the "Insert" button to add code directly to your editor

### Code Operations
- **Explain Code**: Select any code block and use `Ctrl+Shift+E` to get a detailed explanation
- **Refactor Code**: Select code and choose "Refactor Code" from the context menu
- **Generate Docs**: Select functions/classes and generate comprehensive documentation
- **Optimize Code**: Get performance improvement suggestions for selected code

## ⚙️ Configuration

### Extension Settings

Access settings via `File > Preferences > Settings` and search for "Codenjkoiin":

| Setting | Description | Default |
|---------|-------------|---------|
| `aiCodeAssistant.provider` | AI provider (openai, anthropic, custom) | `openai` |
| `aiCodeAssistant.model` | AI model to use | `gpt-4` |
| `aiCodeAssistant.maxTokens` | Maximum tokens for responses | `2048` |
| `aiCodeAssistant.temperature` | Creativity level (0-2) | `0.3` |
| `aiCodeAssistant.includeContext` | Include surrounding code context | `true` |
| `aiCodeAssistant.contextLines` | Number of context lines | `10` |
| `aiCodeAssistant.autoSave` | Auto-save after AI modifications | `false` |

### Supported AI Models

#### OpenAI
- `gpt-4` (Recommended)
- `gpt-4-turbo`
- `gpt-3.5-turbo`

#### Anthropic
- `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`
- `claude-3-opus-20240229`

#### Custom Endpoints
Configure your own API endpoint for self-hosted or enterprise AI models.

## 🎯 Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+Shift+G` / `Cmd+Shift+G` | Generate Code |
| `Ctrl+Shift+C` / `Cmd+Shift+C` | Open AI Chat |
| `Ctrl+Shift+E` / `Cmd+Shift+E` | Explain Selected Code |

## 🔧 Commands

All commands are available via the Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`):

- `AI Assistant: Generate Code` - Open the code generation panel
- `AI Assistant: Open AI Chat` - Open the interactive chat interface
- `AI Assistant: Explain Code` - Explain selected code
- `AI Assistant: Refactor Code` - Refactor selected code
- `AI Assistant: Generate Documentation` - Generate docs for selected code
- `AI Assistant: Optimize Code` - Get optimization suggestions
- `AI Assistant: Configure API Key` - Set up your AI provider

## 🛡️ Privacy & Security

### Data Protection
- **No Code Storage**: Your code never leaves your machine except for the specific AI API calls
- **Secure Key Management**: API keys are stored using VS Code's secure secrets API
- **No Telemetry**: We don't collect or store any usage data or code snippets
- **Local Processing**: All UI and logic runs locally in your VS Code instance

### API Key Security
- Keys are encrypted and stored securely by VS Code
- Keys are never logged or transmitted to third parties
- Easy key rotation and management through the settings UI
- Support for environment variables and external key management

## 🚨 Troubleshooting

### Common Issues

#### "No API key configured" Error
1. Run `AI Assistant: Configure API Key` from the Command Palette
2. Select your AI provider and enter a valid API key
3. Verify the key format matches your provider's requirements

#### API Request Failures
1. Check your internet connection
2. Verify your API key is valid and has sufficient credits
3. Check if your provider's API is experiencing downtime
4. Try reducing the `maxTokens` setting if requests are too large

#### Extension Not Loading
1. Restart VS Code
2. Check the Output panel for error messages
3. Ensure you have the latest version of VS Code
4. Try disabling other extensions to check for conflicts

#### Poor Code Generation Quality
1. Be more specific in your prompts
2. Include relevant context by enabling "Include editor context"
3. Try adjusting the temperature setting (lower = more focused)
4. Specify the exact framework or library you're using

### Getting Help
- Check the [GitHub Issues](https://github.com/your-username/ai-code-assistant/issues) page
- Join our [Discord Community](https://discord.gg/your-invite)
- Email support: <EMAIL>

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
1. Clone the repository
2. Run `npm install` to install dependencies
3. Open in VS Code and press `F5` to launch a new Extension Development Host
4. Make your changes and test thoroughly
5. Submit a pull request

## 📝 Changelog

### Version 1.0.0 (Latest)
- 🎉 Initial release
- ✨ Code generation with multiple AI providers
- 💬 Interactive chat interface
- 🔄 Code refactoring and optimization
- 📚 Documentation generation
- 🔐 Secure API key management
- 🎨 Beautiful modern UI
- 📊 Context-aware code analysis

## 🔮 Roadmap

### Upcoming Features
- 🧪 **Code Testing**: Generate unit tests automatically
- 🔍 **Code Review**: AI-powered code review and suggestions
- 📈 **Analytics**: Code quality metrics and insights
- 🌐 **Team Features**: Shared prompts and templates
- 🎯 **Custom Models**: Support for fine-tuned models
- 📱 **Mobile Support**: VS Code mobile compatibility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Thanks to the VS Code team for the excellent extension API
- OpenAI and Anthropic for providing powerful AI models
- The open-source community for inspiration and feedback

## 📞 Support

- **Documentation**: [Full Documentation](https://docs.aicode-assistant.com)
- **Issues**: [GitHub Issues](https://github.com/your-username/ai-code-assistant/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/ai-code-assistant/discussions)
- **Email**: <EMAIL>

---

**Made with ❤️ for developers by developers**

*Transform your coding experience with AI - Download AI Code Assistant today!*
